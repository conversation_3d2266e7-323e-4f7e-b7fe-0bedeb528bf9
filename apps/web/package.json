{"name": "web", "private": true, "version": "2.6.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@nextui-org/react": "^2.2.9", "@tanstack/react-query": "^4.35.3", "@trpc/client": "^10.45.1", "@trpc/next": "^10.45.1", "@trpc/react-query": "^10.45.1", "autoprefixer": "^10.0.1", "dayjs": "^1.11.10", "framer-motion": "^11.0.5", "next-themes": "^0.2.1", "postcss": "^8", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.2", "sonner": "^1.4.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.4"}}