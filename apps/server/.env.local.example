HOST=0.0.0.0
PORT=4000

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
DATABASE_URL="mysql://root:123456@127.0.0.1:3306/wewe-rss"

# 使用Sqlite
# DATABASE_URL="file:../data/wewe-rss.db"
# DATABASE_TYPE="sqlite"

# 访问授权码
AUTH_CODE=123567

# 每分钟最大请求次数
MAX_REQUEST_PER_MINUTE=60

# 自动提取全文内容
FEED_MODE="fulltext"

# nginx 转发后的服务端地址
SERVER_ORIGIN_URL=http://localhost:4000

# 定时更新订阅源Cron表达式
CRON_EXPRESSION="35 5,17 * * *"

# 是否开启正文html清理
ENABLE_CLEAN_HTML=false

# 连续更新延迟时间(秒)
UPDATE_DELAY_TIME=60

# 读书转发服务，不需要修改
PLATFORM_URL="https://weread.111965.xyz"