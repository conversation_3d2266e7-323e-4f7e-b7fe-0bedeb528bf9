{"name": "server", "version": "2.6.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:migrate:prod": "prisma migrate deploy && npm run start:prod", "postinstall": "npx prisma generate", "migrate": "pnpm prisma migrate dev", "studio": "pnpm prisma studio", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@cjs-exporter/p-map": "^5.5.0", "@nestjs/common": "^10.3.3", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.3.3", "@nestjs/platform-express": "^10.3.3", "@nestjs/schedule": "^4.0.1", "@nestjs/throttler": "^5.1.2", "@prisma/client": "5.10.1", "@trpc/server": "^10.45.1", "axios": "^1.6.7", "cheerio": "1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.10", "express": "^4.18.2", "feed": "^4.2.2", "got": "11.8.6", "hbs": "^4.2.0", "html-minifier": "^4.0.0", "lru-cache": "^10.2.2", "prisma": "^5.10.2", "reflect-metadata": "^0.2.1", "rxjs": "^7.8.1", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.1", "@nestjs/testing": "^10.3.3", "@types/express": "^4.17.21", "@types/html-minifier": "^4.0.5", "@types/jest": "^29.5.12", "@types/node": "^20.11.19", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}