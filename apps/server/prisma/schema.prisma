datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"] // 生成linux可执行文件
}

// 读书账号
model Account {
  id        String    @id @db.VarChar(255)
  token     String    @map("token") @db.VarChar(2048)
  name      String    @map("name") @db.VarChar(1024)
  // 状态 0:失效 1:启用 2:禁用
  status    Int       @default(1) @map("status") @db.Int()
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at")

  @@map("accounts")
}

// 订阅源
model Feed {
  id      String @id @db.VarChar(255)
  mpName  String @map("mp_name") @db.VarChar(512)
  mpCover String @map("mp_cover") @db.Var<PERSON>har(1024)
  mpIntro String @map("mp_intro") @db.Text()
  // 状态 0:失效 1:启用 2:禁用
  status  Int    @default(1) @map("status") @db.Int()

  // article最后同步时间
  syncTime Int @default(0) @map("sync_time")

  // 信息更新时间
  updateTime Int @map("update_time")

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at")

  // 是否有历史文章 1 是  0 否
  hasHistory Int? @default(1) @map("has_history")

  @@map("feeds")
}

model Article {
  id          String @id @db.VarChar(255)
  mpId        String @map("mp_id") @db.VarChar(255)
  title       String @map("title") @db.VarChar(255)
  picUrl      String @map("pic_url") @db.VarChar(255)
  publishTime Int    @map("publish_time")

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at")

  @@map("articles")
}
