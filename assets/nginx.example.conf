server {

  listen 80;

  server_name yourdomain;
  
  location / {

    proxy_pass http://127.0.0.1:4000;
    proxy_http_version 	1.1;
    proxy_set_header	Connection		"";
    proxy_set_header   	Host			$http_host;
    proxy_set_header 	X-Forwarded-Proto 	$scheme;
    proxy_set_header   	X-Real-IP          	$remote_addr;
    proxy_set_header   	X-Forwarded-For    	$proxy_add_x_forwarded_for;
    proxy_set_header    Accept-Encoding gzip;

    proxy_buffering off;
    proxy_cache off;
    
    send_timeout 300;
    proxy_connect_timeout 300;
    proxy_send_timeout 300;
    proxy_read_timeout 300;
  }

}


