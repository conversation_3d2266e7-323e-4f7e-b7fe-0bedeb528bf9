# Lovable 开发提示词集合

## 🚀 项目初始化提示词

### 提示词1: 项目创建
```
创建一个现代简约风格的个人博客网站，参考 https://chenge.ink/ 的设计风格。

技术要求：
- 使用 React + TypeScript
- 集成 Tailwind CSS 进行样式设计
- 响应式设计，支持移动端
- 使用现代化的组件架构

设计风格：
- 极简主义设计理念
- 以白色为主的配色方案
- 大量留白，清晰的视觉层次
- 现代简约的界面风格

项目名称：个人博客网站
网站标语：心有山海，静而无边
```

## 🏠 首页开发提示词

### 提示词2: 首页布局设计
```
为博客网站创建首页，包含以下元素：

顶部导航栏：
- 网站标题"尘世の歌"（左侧）
- 导航菜单：首页、搜索、归档（右侧）
- 简洁的水平布局，白色背景

个人介绍区域：
- 居中布局的个人头像（圆形，80px）
- 姓名"陈源泉"
- 个人标语"心有山海，静而无边"
- 社交媒体图标链接（GitHub、Telegram、Email、RSS）

主要内容区域：
- 左侧：文章列表（占3/4宽度）
- 右侧：侧边栏（占1/4宽度）
- 响应式设计：移动端单列布局

使用 Tailwind CSS 实现，确保简洁优雅的视觉效果。
```

### 提示词3: 动态效果实现
```
在关于页面添加特殊动画效果：

文字轮播动画：
- 创建一个容器，高度50px，居中显示
- 三个文字依次显示："悦己"、"越山"、"阅书"
- 每个文字有不同的背景色：lightcoral、skyblue、lightgreen
- 文字白色，圆角背景，每2秒切换一次
- 使用CSS动画实现平滑过渡效果

渐变背景容器：
- 创建一个动态渐变背景的容器
- 渐变色：coral、crimson、cyan、cornflowerblue、fuchsia
- 背景以5秒为周期循环变化
- 容器内显示随机励志句子
- 文字居中，白色字体

使用CSS-in-JS或Tailwind CSS实现这些动画效果。
```

## 📝 文章系统提示词

### 提示词4: 文章卡片组件
```
创建一个文章卡片组件，用于首页和列表页展示：

卡片设计：
- 白色背景，圆角边框，轻微阴影
- 悬停时有轻微上移和阴影加深效果
- 响应式设计，移动端全宽显示

卡片内容：
- 顶部：文章封面图（16:9比例，可选）
- 标题：18px字体，加粗，深灰色
- 摘要：14px字体，浅灰色，最多3行
- 底部元信息：
  - 左侧：发布日期（小字体，浅灰色）
  - 右侧：标签列表（小圆角背景，可点击）

交互效果：
- 整个卡片可点击进入文章详情
- 标签点击跳转到对应标签页面
- 平滑的悬停动画效果

使用TypeScript定义props接口，支持title、excerpt、date、slug、coverImage、tags等属性。
```

### 提示词5: 文章详情页
```
创建文章详情页面，提供优秀的阅读体验：

页面布局：
- 顶部：文章标题（大字体，加粗）
- 元信息：发布日期、标签、阅读时间估算
- 主要内容：Markdown渲染的文章内容
- 底部：相关文章推荐

阅读体验优化：
- 合适的行高和字间距
- 最大宽度限制，避免行过长
- 代码块语法高亮
- 图片响应式处理
- 标题自动生成锚点

侧边功能：
- 文章目录导航（长文章）
- 返回顶部按钮
- 阅读进度指示器

相关推荐：
- 底部显示3-4篇相关文章
- 使用相同的文章卡片组件
- 基于标签或分类推荐

使用现代的排版设计，确保在各种设备上都有良好的阅读体验。
```

## 🗂️ 内容组织提示词

### 提示词6: 归档页面
```
创建时间线式的归档页面：

页面结构：
- 页面标题"文章归档"
- 按年份分组的文章列表
- 每年显示文章总数

时间线设计：
- 左侧：年份标题（大字体，加粗）
- 右侧：该年份的文章列表
- 文章项：日期 + 文章标题（链接）
- 简洁的分隔线设计

交互功能：
- 年份可折叠/展开
- 默认展开最近两年
- 文章标题点击跳转到详情页
- 显示每年的文章数量统计

响应式设计：
- 桌面端：左右布局
- 移动端：上下堆叠布局
- 保持清晰的视觉层次

使用简洁的设计语言，突出时间线的概念。
```

### 提示词7: 标签和分类系统
```
创建标签云和分类页面：

标签云页面：
- 页面标题"标签"
- 标签以不同大小显示（基于文章数量）
- 颜色渐变或随机颜色
- 悬停效果和点击跳转

分类页面：
- 网格布局显示所有分类
- 每个分类卡片包含：
  - 分类名称
  - 文章数量
  - 最新文章标题
  - 分类描述（可选）

分类/标签详情页：
- 面包屑导航
- 分类/标签介绍
- 该分类下的文章列表
- 使用文章卡片组件展示

交互设计：
- 标签大小反映热度
- 平滑的悬停动画
- 清晰的导航路径
- 支持搜索和筛选

确保设计一致性，与整体网站风格保持统一。
```

## 🔍 搜索功能提示词

### 提示词8: 全站搜索
```
实现全站搜索功能：

搜索框设计：
- 顶部导航栏的搜索图标
- 点击展开搜索框，或跳转到搜索页面
- 支持快捷键 Ctrl+K 或 / 打开搜索
- 现代化的搜索框样式

搜索页面：
- 大型搜索框居中显示
- 实时搜索建议（可选）
- 搜索历史记录
- 热门搜索词推荐

搜索结果：
- 结果按相关性排序
- 关键词高亮显示
- 显示匹配的文章标题、摘要
- 搜索结果数量统计
- 无结果时的友好提示

搜索算法：
- 支持标题、内容、标签搜索
- 模糊匹配和精确匹配
- 搜索结果分页显示
- 搜索性能优化

使用现代的搜索UI设计，提供流畅的搜索体验。
```

## 👤 个人页面提示词

### 提示词9: 关于页面
```
创建个人介绍页面，展示个人信息和理念：

页面结构：
- 个人头像和基本信息
- 个人介绍和理念
- 年度目标和规划
- 联系方式和社交媒体

特殊效果：
- 文字轮播动画："悦己"、"越山"、"阅书"
- 渐变背景的励志语录容器
- 平滑的滚动动画效果

内容区域：
- 个人简介（支持Markdown格式）
- 工作目标和学习计划
- 生活理念和价值观
- 技能和兴趣爱好

设计风格：
- 与网站整体风格保持一致
- 适当的留白和排版
- 突出个人特色和个性
- 响应式设计

确保页面既专业又有个人特色，体现博主的个性和理念。
```

## 📱 响应式设计提示词

### 提示词10: 移动端优化
```
优化网站的移动端体验：

导航优化：
- 汉堡菜单替代水平导航
- 侧滑菜单或下拉菜单
- 搜索功能移动端适配

布局调整：
- 单列布局替代多列
- 侧边栏在移动端隐藏或底部显示
- 文章卡片全宽显示
- 合适的间距和字体大小

交互优化：
- 触摸友好的按钮大小
- 滑动手势支持
- 快速加载和流畅滚动
- 移动端特有的交互模式

性能优化：
- 图片懒加载
- 代码分割
- 首屏快速加载
- 离线功能支持（PWA）

确保在各种移动设备上都有优秀的用户体验。
```

## 🎨 组件库提示词

### 提示词11: 基础组件库
```
创建可复用的基础组件库：

按钮组件：
- 主要按钮、次要按钮、文本按钮
- 不同尺寸：小、中、大
- 加载状态和禁用状态
- 图标按钮支持

表单组件：
- 输入框、文本域、选择框
- 表单验证和错误提示
- 搜索框特殊样式
- 标签输入组件

布局组件：
- 容器组件（最大宽度限制）
- 网格系统（响应式）
- 卡片组件（不同样式）
- 分隔符和间距组件

导航组件：
- 顶部导航栏
- 面包屑导航
- 分页组件
- 标签组件

所有组件都要：
- 使用TypeScript定义接口
- 支持主题定制
- 响应式设计
- 可访问性支持

建立一致的设计系统，确保组件的复用性和一致性。
```

## 🔧 数据管理提示词

### 提示词12: 数据结构和API
```
设计博客的数据结构和API：

文章数据结构：
```typescript
interface Post {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  coverImage?: string
  publishedAt: string
  updatedAt: string
  tags: string[]
  category: string
  readingTime: number
  featured: boolean
}
```

分类和标签：
```typescript
interface Category {
  id: string
  name: string
  slug: string
  description?: string
  postCount: number
}

interface Tag {
  id: string
  name: string
  slug: string
  postCount: number
}
```

API函数：
- getAllPosts() - 获取所有文章
- getPostBySlug(slug) - 根据slug获取文章
- getPostsByCategory(category) - 按分类获取文章
- getPostsByTag(tag) - 按标签获取文章
- searchPosts(query) - 搜索文章
- getFeaturedPosts() - 获取推荐文章

数据存储：
- 使用JSON文件存储示例数据
- 支持Markdown文件解析
- 实现数据缓存机制
- 为将来的CMS集成预留接口

确保数据结构的可扩展性和API的易用性。
```

## 📊 SEO和性能提示词

### 提示词13: SEO优化
```
实现全面的SEO优化：

Meta标签管理：
- 动态生成页面标题
- 描述标签优化
- 关键词标签
- Open Graph标签
- Twitter Card标签

结构化数据：
- 文章的JSON-LD标记
- 面包屑结构化数据
- 作者信息标记
- 网站信息标记

技术SEO：
- 语义化HTML标签
- 正确的标题层级
- 图片alt属性
- 内部链接优化
- 网站地图生成

性能优化：
- 图片优化和懒加载
- 代码分割和压缩
- 缓存策略
- Core Web Vitals优化

确保网站在搜索引擎中有良好的表现和排名。
```

## 🚀 部署提示词

### 提示词14: 生产环境配置
```
准备网站的生产环境部署：

构建优化：
- 生产环境构建配置
- 代码压缩和优化
- 静态资源处理
- 环境变量管理

部署配置：
- 支持Vercel、Netlify等平台
- 自动化部署流程
- 域名和SSL配置
- CDN配置

监控和分析：
- Google Analytics集成
- 错误监控设置
- 性能监控
- 用户行为分析

安全配置：
- HTTPS强制跳转
- 安全头设置
- XSS防护
- CSRF保护

确保网站在生产环境中稳定、安全、高性能运行。
```

## 💡 使用建议

### 提示词使用顺序：
1. **项目初始化**: 提示词1
2. **核心页面**: 提示词2-5
3. **功能完善**: 提示词6-9
4. **响应式优化**: 提示词10
5. **组件库**: 提示词11
6. **数据管理**: 提示词12
7. **SEO和性能**: 提示词13
8. **部署准备**: 提示词14

### 与Lovable交互技巧：
- 每次提交一个完整的功能模块
- 提供具体的设计要求和技术细节
- 包含示例数据和预期效果
- 明确响应式设计要求
- 强调用户体验和性能优化
