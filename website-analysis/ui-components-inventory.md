# UI组件清单与设计规范

## 1. 按钮组件 (Buttons)

### 1.1 主要按钮 (<PERSON> Button)
```css
.primary-button {
  background-color: rgb(230, 46, 77);
  color: rgb(255, 255, 255);
  border: 2px solid rgb(255, 255, 255);
  border-radius: 16px;
  padding: 8px 20px;
  font-size: 20px;
  font-weight: normal;
}
```
**用途**: 主要操作按钮，如"开始阅览"

### 1.2 工具栏按钮 (<PERSON><PERSON><PERSON>)
```css
.toolbar-button {
  background-color: transparent;
  color: rgb(217, 137, 130);
  border: none;
  border-radius: 8px;
  padding: 0px;
  font-size: 16px;
}

.toolbar-button:hover {
  background-color: rgba(217, 137, 130, 0.1);
}

.toolbar-button.selected {
  background-color: rgb(217, 137, 130);
  color: rgb(25, 28, 35);
}
```
**用途**: 导航中心、文库浏览、激光指针等工具按钮

### 1.3 辅助按钮 (Assistant <PERSON><PERSON>)
```css
.assistant-button {
  background-color: transparent;
  color: rgb(217, 137, 130);
  border: none;
  border-radius: 12px;
  padding: 0px;
  font-size: 16px;
}
```
**用途**: 复制、表格十字、换行、画中画等辅助功能

### 1.4 关闭按钮 (Close Button)
```css
.close-button {
  background-color: rgb(25, 28, 35);
  color: rgb(255, 255, 255);
  border: none;
  border-radius: 0px 0px 0px 50px;
  padding: 12px 12px 8px 8px;
}
```
**用途**: 关闭浮动面板

## 2. 卡片组件 (Cards)

### 2.1 深色浮动卡片 (Dark Float Card)
```css
.v-float-card {
  background-color: rgb(25, 28, 35);
  border: none;
  border-radius: 16px;
  padding: 10px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 4px 16px 0px, 
              rgba(255, 255, 255, 0.2) 0px 0px 0px 1px inset;
  position: fixed;
  z-index: 100;
}
```
**用途**: 工具栏、章节导航等主要功能面板

### 2.2 浅色浮动卡片 (Light Float Card)
```css
.v-float-card2 {
  background-color: rgb(235, 237, 239);
  border: none;
  border-radius: 9px;
  padding: 1px 3px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 4px 16px 0px, 
              rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset;
}
```
**用途**: 状态栏、历史记录等次要信息面板

### 2.3 信息提示卡片 (Info Tips Card)
```css
.v-info-tips {
  background-color: rgb(235, 237, 239);
  border: 1px solid rgb(165, 171, 174);
  border-radius: 0px 16px 16px;
  padding: 10px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 4px 16px 0px, 
              rgba(255, 255, 255, 0.2) 0px 0px 0px 1px inset;
}
```
**用途**: 提示信息、帮助文档

### 2.4 圆形按钮卡片 (Circular Button Card)
```css
.v-pip-btn {
  background-color: rgb(255, 255, 255);
  border: none;
  border-radius: 50%;
  padding: 0px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 4px 16px 0px, 
              rgba(255, 255, 255, 0.2) 0px 0px 0px 1px inset;
}
```
**用途**: 缩放、关闭等圆形操作按钮

## 3. 导航组件 (Navigation)

### 3.1 固定工具栏 (Fixed Toolbar)
```css
.v-toolbar {
  position: fixed;
  width: 300px;
  height: 50px;
  background-color: rgb(25, 28, 35);
  border-radius: 16px;
  padding: 10px;
  z-index: 100;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}
```

### 3.2 章节导航 (Chapter Navigation)
```css
.v-chapter-nav {
  position: fixed;
  background-color: rgb(25, 28, 35);
  border-radius: 16px;
  padding: 0px;
  z-index: 100;
  /* 包含上一章、当前章节、下一章导航 */
}
```

### 3.3 侧边栏 (Sidebar)
```css
.sidebar {
  position: fixed;
  background-color: rgb(235, 237, 239);
  z-index: 4000;
  /* 可收缩展开的侧边栏 */
}
```

## 4. 文本组件 (Typography)

### 4.1 标题层级 (Headings)
```css
h1 {
  font-size: 28px;
  font-weight: 700;
  color: rgb(217, 137, 130);
  margin-top: 84px;
  margin-bottom: 0px;
}

h2 {
  font-size: 26px;
  font-weight: 700;
  color: rgb(217, 137, 130);
  margin-top: 78px;
  margin-bottom: 0px;
}

h3 {
  font-size: 24px;
  font-weight: 700;
  color: rgb(217, 137, 130);
  margin-top: 48px;
  margin-bottom: 0px;
}
```

### 4.2 正文文本 (Body Text)
```css
body {
  font-family: color-emoji, "VLOOK Digital local", -apple-system, 
               Roboto, "SF Pro Text", "SF Pro Display", 
               "PingFang SC", "PingFang TC", "PingFang HK";
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: rgb(28, 30, 31);
  background-color: rgb(255, 255, 255);
}
```

## 5. 表单组件 (Forms)

### 5.1 文本输入框 (Text Input)
```css
.v-textfield {
  /* 搜索框和过滤输入框样式 */
  background-color: transparent;
  border: 1px solid rgb(165, 171, 174);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
}
```

### 5.2 分段控制器 (Segment Control)
```css
.v-segment-btn {
  background-color: transparent;
  border-radius: 6px;
  padding: 0px;
  font-size: 16px;
}

.v-segment-btn.selected {
  background-color: rgb(25, 28, 35);
}
```

## 6. 特效组件 (Effects)

### 6.1 过渡动画 (Transitions)
```css
.v-transition-all {
  transition: all 0.3s ease;
}
```

### 6.2 背景模糊 (Backdrop Blur)
```css
.v-backdrop-blurs {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
```

### 6.3 遮罩层 (Mask)
```css
.v-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
```

## 7. 图标系统 (Icons)

### 7.1 SVG图标
- 使用SVG sprite系统
- 图标尺寸: 18px x 20px (小图标)
- 颜色: rgb(217, 137, 130) (浅色主题)

### 7.2 常用图标
- 导航中心 (#icoNavCenter)
- 文库浏览 (#icoDocLib)
- 激光指针、聚光灯、段落导航等功能图标

## 8. 响应式断点 (Responsive Breakpoints)

```css
/* 移动设备 */
@media (max-width: 768px) {
  .v-toolbar {
    width: calc(100% - 40px);
    left: 20px;
    transform: none;
  }
}

/* 平板设备 */
@media (max-width: 1024px) {
  /* 中等屏幕适配 */
}

/* 桌面设备 */
@media (min-width: 1025px) {
  /* 大屏幕优化 */
}
```

---

*此组件清单涵盖了网站的所有主要UI元素，可直接用于开发参考*
