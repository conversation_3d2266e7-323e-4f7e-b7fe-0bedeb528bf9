# 开发指南

## 项目初始化

### 技术栈选择
基于分析结果，推荐使用以下技术栈来克隆该网站：

```json
{
  "framework": "Next.js 14+",
  "styling": "Tailwind CSS + Custom CSS",
  "content": "Markdown + MDX",
  "deployment": "Vercel",
  "cms": "Notion API (可选)",
  "analytics": "Google Analytics"
}
```

### 项目结构
```
blog-clone/
├── public/
│   ├── images/
│   ├── icons/
│   └── favicon.ico
├── src/
│   ├── components/
│   │   ├── ui/           # 基础UI组件
│   │   ├── layout/       # 布局组件
│   │   └── features/     # 功能组件
│   ├── pages/
│   │   ├── api/          # API路由
│   │   ├── article/      # 文章页面
│   │   └── ...
│   ├── styles/
│   │   ├── globals.css
│   │   └── components.css
│   ├── lib/              # 工具函数
│   ├── data/             # 静态数据
│   └── types/            # TypeScript类型定义
├── content/              # Markdown文章
├── package.json
└── next.config.js
```

## 开发步骤

### 第一阶段：基础框架搭建
1. **初始化Next.js项目**
```bash
npx create-next-app@latest blog-clone --typescript --tailwind --eslint
cd blog-clone
```

2. **安装必要依赖**
```bash
npm install @next/mdx @mdx-js/loader @mdx-js/react
npm install gray-matter remark remark-html
npm install date-fns clsx
npm install @types/node
```

3. **配置Tailwind CSS**
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: '#007bff',
        secondary: '#6c757d',
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
```

### 第二阶段：基础组件开发
1. **布局组件**
```typescript
// src/components/layout/Layout.tsx
import Header from './Header'
import Footer from './Footer'
import Sidebar from './Sidebar'

interface LayoutProps {
  children: React.ReactNode
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <main className="lg:col-span-3">
            {children}
          </main>
          <aside className="lg:col-span-1">
            <Sidebar />
          </aside>
        </div>
      </div>
      <Footer />
    </div>
  )
}
```

2. **文章卡片组件**
```typescript
// src/components/ui/ArticleCard.tsx
import Link from 'next/link'
import Image from 'next/image'
import { formatDate } from '@/lib/utils'

interface ArticleCardProps {
  title: string
  excerpt: string
  date: string
  slug: string
  coverImage?: string
  tags: string[]
}

export default function ArticleCard({
  title,
  excerpt,
  date,
  slug,
  coverImage,
  tags
}: ArticleCardProps) {
  return (
    <article className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-200">
      {coverImage && (
        <div className="aspect-video relative">
          <Image
            src={coverImage}
            alt={title}
            fill
            className="object-cover"
          />
        </div>
      )}
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-2">
          <Link href={`/article/${slug}`} className="hover:text-blue-600">
            {title}
          </Link>
        </h2>
        <p className="text-gray-600 mb-4 line-clamp-3">{excerpt}</p>
        <div className="flex justify-between items-center">
          <time className="text-sm text-gray-500">
            {formatDate(date)}
          </time>
          <div className="flex gap-2">
            {tags.map(tag => (
              <span
                key={tag}
                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    </article>
  )
}
```

### 第三阶段：页面开发
1. **首页开发**
```typescript
// src/pages/index.tsx
import Layout from '@/components/layout/Layout'
import ArticleCard from '@/components/ui/ArticleCard'
import { getAllPosts } from '@/lib/api'
import { Post } from '@/types'

interface HomeProps {
  posts: Post[]
}

export default function Home({ posts }: HomeProps) {
  return (
    <Layout>
      <div className="space-y-8">
        <section className="text-center py-12">
          <h1 className="text-4xl font-bold mb-4">尘世の歌</h1>
          <p className="text-xl text-gray-600">心有山海，静而无边</p>
        </section>
        
        <section>
          <h2 className="text-2xl font-bold mb-6">最新文章</h2>
          <div className="grid gap-6">
            {posts.map(post => (
              <ArticleCard
                key={post.slug}
                title={post.title}
                excerpt={post.excerpt}
                date={post.date}
                slug={post.slug}
                coverImage={post.coverImage}
                tags={post.tags}
              />
            ))}
          </div>
        </section>
      </div>
    </Layout>
  )
}

export async function getStaticProps() {
  const posts = getAllPosts(['title', 'date', 'slug', 'excerpt', 'coverImage', 'tags'])
  
  return {
    props: {
      posts,
    },
  }
}
```

2. **文章详情页**
```typescript
// src/pages/article/[slug].tsx
import Layout from '@/components/layout/Layout'
import { getPostBySlug, getAllPosts } from '@/lib/api'
import { markdownToHtml } from '@/lib/markdownToHtml'
import { Post } from '@/types'

interface PostPageProps {
  post: Post
}

export default function PostPage({ post }: PostPageProps) {
  return (
    <Layout>
      <article className="prose prose-lg max-w-none">
        <header className="mb-8">
          <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
          <div className="flex items-center gap-4 text-gray-600">
            <time>{post.date}</time>
            <div className="flex gap-2">
              {post.tags.map(tag => (
                <span key={tag} className="px-2 py-1 bg-gray-100 rounded-full text-sm">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </header>
        <div dangerouslySetInnerHTML={{ __html: post.content }} />
      </article>
    </Layout>
  )
}

export async function getStaticProps({ params }: { params: { slug: string } }) {
  const post = getPostBySlug(params.slug, [
    'title',
    'date',
    'slug',
    'content',
    'tags',
    'coverImage',
  ])
  const content = await markdownToHtml(post.content || '')

  return {
    props: {
      post: {
        ...post,
        content,
      },
    },
  }
}

export async function getStaticPaths() {
  const posts = getAllPosts(['slug'])

  return {
    paths: posts.map((post) => {
      return {
        params: {
          slug: post.slug,
        },
      }
    }),
    fallback: false,
  }
}
```

### 第四阶段：特殊效果实现
1. **动画文字组件**
```typescript
// src/components/ui/AnimatedText.tsx
import { useEffect, useState } from 'react'

const texts = ['悦己', '越山', '阅书']
const colors = ['bg-red-400', 'bg-blue-400', 'bg-green-400']

export default function AnimatedText() {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % texts.length)
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="h-12 overflow-hidden flex items-center justify-center">
      <div
        className={`${colors[currentIndex]} text-white px-4 py-2 rounded transition-all duration-500 transform`}
        style={{
          animation: 'slide 6s linear infinite'
        }}
      >
        {texts[currentIndex]}
      </div>
    </div>
  )
}
```

2. **渐变背景组件**
```typescript
// src/components/ui/GradientBackground.tsx
interface GradientBackgroundProps {
  children: React.ReactNode
}

export default function GradientBackground({ children }: GradientBackgroundProps) {
  return (
    <div className="gradient-bg p-4 rounded-lg text-white text-center">
      {children}
      <style jsx>{`
        .gradient-bg {
          background: linear-gradient(-45deg, coral, crimson, cyan, cornflowerblue, fuchsia);
          background-size: 1000% 1000%;
          animation: gradient 5s ease infinite;
        }
        
        @keyframes gradient {
          0% { background-position: 0 50%; }
          50% { background-position: 30% 50%; }
          100% { background-position: 0 50%; }
        }
      `}</style>
    </div>
  )
}
```

### 第五阶段：内容管理
1. **Markdown文章处理**
```typescript
// src/lib/api.ts
import fs from 'fs'
import { join } from 'path'
import matter from 'gray-matter'

const postsDirectory = join(process.cwd(), 'content/posts')

export function getPostSlugs() {
  return fs.readdirSync(postsDirectory)
}

export function getPostBySlug(slug: string, fields: string[] = []) {
  const realSlug = slug.replace(/\.md$/, '')
  const fullPath = join(postsDirectory, `${realSlug}.md`)
  const fileContents = fs.readFileSync(fullPath, 'utf8')
  const { data, content } = matter(fileContents)

  const items: any = {}

  fields.forEach((field) => {
    if (field === 'slug') {
      items[field] = realSlug
    }
    if (field === 'content') {
      items[field] = content
    }
    if (typeof data[field] !== 'undefined') {
      items[field] = data[field]
    }
  })

  return items
}

export function getAllPosts(fields: string[] = []) {
  const slugs = getPostSlugs()
  const posts = slugs
    .map((slug) => getPostBySlug(slug, fields))
    .sort((post1, post2) => (post1.date > post2.date ? -1 : 1))
  return posts
}
```

### 第六阶段：部署配置
1. **Vercel部署配置**
```json
// vercel.json
{
  "build": {
    "env": {
      "NEXT_PUBLIC_SITE_URL": "https://your-domain.com"
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        }
      ]
    }
  ]
}
```

2. **SEO配置**
```typescript
// src/components/SEO.tsx
import Head from 'next/head'

interface SEOProps {
  title: string
  description: string
  image?: string
  url?: string
}

export default function SEO({ title, description, image, url }: SEOProps) {
  const siteTitle = '尘世の歌'
  const fullTitle = title ? `${title} - ${siteTitle}` : siteTitle

  return (
    <Head>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      {image && <meta property="og:image" content={image} />}
      {url && <meta property="og:url" content={url} />}
      <meta name="twitter:card" content="summary_large_image" />
    </Head>
  )
}
```

## 开发注意事项

### 性能优化
1. **图片优化**: 使用Next.js的Image组件
2. **代码分割**: 使用动态导入
3. **缓存策略**: 配置适当的缓存头
4. **懒加载**: 非关键内容使用懒加载

### 可访问性
1. **语义化HTML**: 使用正确的HTML标签
2. **键盘导航**: 确保所有交互元素可通过键盘访问
3. **屏幕阅读器**: 提供适当的ARIA标签
4. **颜色对比**: 确保足够的颜色对比度

### 测试策略
1. **单元测试**: 使用Jest和React Testing Library
2. **集成测试**: 测试页面和组件交互
3. **端到端测试**: 使用Playwright或Cypress
4. **性能测试**: 使用Lighthouse进行性能评估

## 部署清单

- [ ] 配置环境变量
- [ ] 设置自定义域名
- [ ] 配置SSL证书
- [ ] 设置CDN加速
- [ ] 配置分析工具
- [ ] 设置错误监控
- [ ] 配置备份策略
- [ ] 测试所有功能
- [ ] 性能优化检查
- [ ] SEO优化验证
