# 网站分析与开发文档生成 - 优化交互提示词模板

## 📋 对话质量评估

### 🔍 识别的问题和改进点

#### 表达不清晰的环节
1. **初始阶段缺乏明确框架**
   - 问题：没有提前说明完整的分析维度和预期输出
   - 改进：开始就建立清晰的分析框架和阶段划分

2. **工具使用逻辑不够清晰**
   - 问题：工具使用的顺序和目的没有提前说明
   - 改进：明确每个工具的作用、使用时机和预期结果

3. **文档关联关系说明滞后**
   - 问题：文档间的依赖关系在后期才说明
   - 改进：在创建文档时就说明其在整体架构中的位置

#### 不够具体的地方
1. **分析标准模糊**
   - 问题："详细分析"的具体标准不明确
   - 改进：提供具体的分析维度和深度要求

2. **验收标准缺失**
   - 问题：每个阶段的完成标准不够明确
   - 改进：为每个阶段设定具体的验收标准

#### 效率优化空间
1. **可以更加并行化**：某些分析工作可以同时进行
2. **可以更加模板化**：建立标准的文档模板
3. **可以更加自动化**：减少重复性工作

## 🎯 方法论总结

### 标准化分析流程
```
阶段1: 需求理解 → 阶段2: 工具准备 → 阶段3: 多维分析 → 阶段4: 设计规范 → 阶段5: 实施规划 → 阶段6: 文档整合
   ↓              ↓              ↓              ↓              ↓              ↓
目标确认        工具配置        深度分析        组件设计        任务拆解        关联梳理
用户反馈        网站探索        技术评估        架构设计        AI适配         使用指南
```

### 最佳工具组合使用方式
- **Firecrawl**: 网站结构映射 + 内容抓取 + SEO分析
- **Puppeteer**: 界面截图 + 交互测试 + 视觉分析
- **mcp-feedback-enhanced**: 阶段性反馈 + 需求确认 + 质量验证

### 文档体系组织原则
1. **分层架构**: 分析层 → 设计层 → 实施层
2. **单一职责**: 每个文档专注一个主题
3. **关联清晰**: 明确文档间的依赖关系
4. **实用导向**: 所有分析都要转化为可执行任务

## 🚀 优化交互提示词模板

### 模板使用说明
```
适用场景：网站分析与AI辅助开发文档生成
使用工具：Augment Code + Firecrawl MCP + Puppeteer MCP + mcp-feedback-enhanced
预期输出：完整的网站分析文档体系 + AI开发指导
预计时间：2-3天（根据网站复杂度）
```

---

## 📝 完整交互提示词

### 🎯 项目目标定义

我需要你作为专业的网站分析师和开发顾问，使用 Augment Code 平台的 MCP 工具能力，对指定网站进行全方位深度分析，并生成完整的开发文档体系。

**核心目标**：
1. **深度分析目标网站**：从UI/UX设计、技术架构、用户体验等多个维度进行专业分析
2. **生成可执行文档**：输出能够直接指导开发的详细文档和实施方案
3. **AI开发适配**：为AI辅助开发平台（如Lovable）提供结构化的开发指导

**分析网站**：[用户提供的网站URL]
**分析目的**：[网站克隆/竞品分析/设计参考/技术学习]

### 📋 标准化执行流程

#### 阶段1: 需求理解与框架建立 (30分钟)
**执行步骤**：
1. **确认分析目标**：明确用户的具体需求和预期输出
2. **建立分析框架**：说明将要进行的5个维度分析
3. **工具准备说明**：解释将要使用的MCP工具及其作用
4. **调用 mcp-feedback-enhanced**：确认分析框架和用户期望

**预期输出**：
- 明确的项目目标和范围
- 完整的分析维度框架
- 用户确认的执行计划

**质量标准**：
- 目标明确，可衡量
- 框架完整，覆盖全面
- 用户理解并确认

#### 阶段2: 网站探索与工具部署 (45分钟)
**执行步骤**：
1. **使用 Firecrawl 进行网站映射**
   ```
   调用 firecrawl_map_firecrawl-mcp
   参数：url=[目标网站], limit=50, includeSubdomains=false
   目的：获取网站完整的URL结构和页面类型
   ```

2. **使用 Firecrawl 抓取关键页面内容**
   ```
   调用 firecrawl_scrape_firecrawl-mcp
   页面：首页、关于页、文章页、列表页等
   格式：["markdown", "screenshot"]
   目的：获取页面内容和结构信息
   ```

3. **使用 Puppeteer 进行界面分析**
   ```
   调用 puppeteer_navigate_puppeteer + puppeteer_screenshot_puppeteer
   页面：主要页面类型
   尺寸：1920x1080 (桌面端)
   目的：获取高质量的界面截图用于设计分析
   ```

4. **调用 mcp-feedback-enhanced**：展示初步探索结果，获取用户反馈

**预期输出**：
- 完整的网站结构图
- 关键页面的内容和截图
- 初步的页面类型分类

**质量标准**：
- 网站结构完整，无遗漏
- 截图清晰，能够进行设计分析
- 内容抓取准确，格式规范

#### 阶段3: 五维度深度分析 (2-3小时)
**执行步骤**：

**3.1 网站结构分析**
- 创建 `site-structure.md`
- 分析页面架构、导航体系、URL规律、信息架构
- 绘制页面关系图和导航流程

**3.2 UI设计分析**
- 创建 `ui-design-analysis.md`
- 分析设计风格、色彩系统、字体排版、布局规范
- 提取动画效果和视觉特色

**3.3 交互设计分析**
- 创建 `interaction-design.md`
- 分析用户体验、交互流程、导航模式、特殊功能
- 评估可访问性和性能优化

**3.4 技术实现分析**
- 创建 `technical-analysis.md`
- 推测技术栈、分析性能优化、SEO策略、部署方案
- 评估开发复杂度和技术难点

**3.5 后端架构分析**
- 创建 `backend-architecture.md`
- 设计数据库结构、API接口、认证方案
- 提供多种技术方案对比

**调用 mcp-feedback-enhanced**：展示分析结果，获取用户反馈和调整建议

**预期输出**：
- 5个专业分析文档
- 每个文档包含具体的分析结论和建议
- 技术方案和实施建议

**质量标准**：
- 分析深度足够，覆盖所有关键要素
- 结论具体，可操作性强
- 技术方案合理，可行性高

#### 阶段4: 设计规范与组件库 (1小时)
**执行步骤**：
1. **创建组件库设计文档**
   - 文件：`component-library.md`
   - 内容：基础组件、布局组件、业务组件、特效组件
   - 包含：设计规范、代码示例、使用指南

2. **建立设计系统**
   - 色彩系统、字体系统、间距系统
   - 响应式设计规范
   - 主题配置方案

**调用 mcp-feedback-enhanced**：确认设计规范的完整性和实用性

**预期输出**：
- 完整的组件库设计文档
- 可复用的设计系统规范
- 具体的实现指导

**质量标准**：
- 组件设计完整，覆盖所有需求
- 设计规范清晰，易于实施
- 代码示例准确，可直接使用

#### 阶段5: 开发实施规划 (1.5小时)
**执行步骤**：
1. **任务拆解**
   - 文件：`tasks.md`
   - 内容：24个具体开发任务，分5个阶段
   - 包含：优先级、时间估算、验收标准

2. **AI平台适配**
   - 文件：`lovable-prompts.md`
   - 内容：14个结构化提示词
   - 包含：具体要求、代码示例、设计规范

3. **使用指南创建**
   - 文件：`lovable-usage-guide.md`
   - 内容：分步骤操作手册
   - 包含：交互技巧、问题解决、进度跟踪

**调用 mcp-feedback-enhanced**：确认实施方案的可行性和完整性

**预期输出**：
- 详细的任务拆解清单
- AI平台的提示词集合
- 完整的使用操作指南

**质量标准**：
- 任务拆解合理，可执行性强
- 提示词结构化，AI友好
- 使用指南详细，易于操作

#### 阶段6: 文档整合与交付 (30分钟)
**执行步骤**：
1. **创建文档索引**
   - 更新 `README.md`
   - 建立文档关联关系图
   - 提供使用流程指导

2. **质量检查**
   - 检查文档完整性
   - 验证关联关系
   - 确认实用性

**调用 mcp-feedback-enhanced**：最终确认交付质量，获取用户满意度反馈

**预期输出**：
- 完整的文档体系索引
- 清晰的使用指南
- 用户满意的交付成果

**质量标准**：
- 文档体系完整，无遗漏
- 关联关系清晰，易于理解
- 用户满意度高，实用性强

### 🔧 工具使用规范

#### Firecrawl MCP 使用规范
**使用时机**：网站结构分析、内容抓取、SEO分析
**参数配置**：
- `formats`: ["markdown", "screenshot"] 用于内容分析
- `onlyMainContent`: true 用于提取核心内容
- `limit`: 50 用于网站映射

**最佳实践**：
- 先进行网站映射，再抓取具体页面
- 重要页面都要抓取内容和截图
- 注意处理大型网站的限制

#### Puppeteer MCP 使用规范
**使用时机**：界面截图、交互测试、视觉分析
**参数配置**：
- `width`: 1920, `height`: 1080 用于桌面端分析
- 不同页面类型都要截图
- 关注响应式设计的表现

**最佳实践**：
- 截图要清晰，能够进行设计分析
- 关注特殊交互效果和动画
- 测试不同设备尺寸的表现

#### mcp-feedback-enhanced 使用规范
**使用时机**：每个阶段结束时，重要决策点
**调用频率**：至少6次（每个阶段1次）
**参数配置**：
- `summary`: 详细说明当前阶段的成果
- `timeout`: 600秒，给用户充分时间反馈

**最佳实践**：
- 及时调用，获取用户反馈
- 根据反馈调整分析方向
- 确保用户满意度

### ✅ 质量控制标准

#### 分析质量标准
- **完整性**：覆盖所有重要维度，无重大遗漏
- **深度性**：分析深入，不停留在表面
- **准确性**：分析结论准确，技术判断正确
- **实用性**：分析结果能够指导实际开发

#### 文档质量标准
- **结构化**：文档结构清晰，层次分明
- **规范化**：命名规范，格式统一
- **关联性**：文档间关系明确，逻辑清晰
- **可操作**：提供具体的操作指导

#### 交付质量标准
- **用户满意**：用户确认满足需求
- **可执行性**：文档能够指导实际开发
- **可维护性**：文档易于更新和维护
- **可复用性**：方法和模板可以复用

### 📦 文档交付规范

#### 文档结构要求
```
website-analysis/
├── README.md                    # 项目概述和文档索引
├── site-structure.md           # 网站结构分析
├── ui-design-analysis.md       # UI设计分析
├── interaction-design.md       # 交互设计分析
├── technical-analysis.md       # 技术实现分析
├── backend-architecture.md     # 后端架构设计
├── component-library.md        # 组件库设计
├── tasks.md                    # 任务拆解清单
├── lovable-prompts.md          # AI开发提示词
└── lovable-usage-guide.md      # 使用操作指南
```

#### 命名规范
- 文件名使用小写字母和连字符
- 文档标题使用中文，简洁明确
- 章节标题使用emoji图标增强可读性

#### 内容规范
- 每个文档都要有明确的目标和范围
- 提供具体的分析结论和建议
- 包含代码示例和操作指导
- 注明文档间的关联关系

### 🛠️ 问题解决机制

#### 常见问题及解决方案
1. **网站访问受限**：使用代理或调整抓取策略
2. **内容抓取不完整**：调整参数或多次尝试
3. **分析深度不够**：增加分析维度或细化分析点
4. **用户需求变化**：及时调整分析方向和重点

#### 质量保证机制
1. **阶段性检查**：每个阶段都要进行质量检查
2. **用户反馈**：及时获取用户反馈并调整
3. **交叉验证**：多个工具和方法相互验证
4. **最终确认**：交付前进行完整性检查

---

## 📊 使用效果评估

### 成功指标
- **分析完整性**：覆盖所有重要维度 ≥95%
- **文档实用性**：用户满意度 ≥90%
- **开发可执行性**：任务可执行率 ≥95%
- **AI适配性**：提示词有效率 ≥90%

### 持续改进
- 收集用户反馈，优化分析方法
- 更新工具使用技巧，提高效率
- 完善文档模板，提升质量
- 扩展适用场景，增强通用性

通过使用这个优化的交互提示词模板，可以确保网站分析项目的质量和效率，为AI辅助开发提供高质量的输入和指导。
