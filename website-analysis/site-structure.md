# 网站结构分析

## 页面架构概览

### 主要页面类型

1. **首页** (`/`)
   - 网站入口和内容展示中心
   - 包含最新文章列表和个人介绍

2. **关于页面** (`/about`)
   - 详细的个人介绍
   - 年度目标和规划
   - 个人理念和价值观

3. **文章页面** (`/article/[slug]`)
   - 具体的博客文章内容
   - 文章元信息和相关推荐

4. **归档页面** (`/archive`)
   - 按时间分类的文章列表
   - 时间线式的内容组织

5. **分类页面** (`/category`)
   - 按主题分类的文章
   - 内容主题导航

6. **标签页面** (`/tag`)
   - 按标签分类的文章
   - 关键词导航

7. **搜索页面** (`/search`)
   - 全站内容搜索功能
   - 搜索结果展示

8. **其他功能页面**
   - 链接页面 (`/links`)
   - 书籍页面 (`/books`)
   - 备忘录页面 (`/memos`)

## 导航结构

### 主导航
- 首页
- 搜索
- 归档

### 次级导航
- 文章
- 分类
- 标签

### 底部导航
- RSS订阅
- 社交媒体链接
- 版权信息

## 信息架构

### 内容层次结构
```
网站根目录
├── 首页（文章列表）
├── 关于页面
├── 内容分类
│   ├── 按时间归档
│   ├── 按主题分类
│   └── 按标签分类
├── 功能页面
│   ├── 搜索
│   ├── 链接
│   ├── 书籍
│   └── 备忘录
└── 文章详情页
```

### URL结构规律
- 文章页面: `/article/post[YYYYMMDD]`
- 功能页面: `/[功能名称]`
- 分类页面: `/category/[分类名]`
- 标签页面: `/tag/[标签名]`

## 内容组织特点

1. **时间维度**: 按发布时间组织内容
2. **主题维度**: 按内容主题分类
3. **标签维度**: 按关键词标记
4. **功能维度**: 按用途分类页面

## 页面关系图

```mermaid
graph TD
    A[首页] --> B[文章列表]
    A --> C[个人介绍]
    B --> D[文章详情]
    D --> E[相关文章]
    A --> F[归档页面]
    F --> G[时间分类]
    A --> H[分类页面]
    H --> I[主题分类]
    A --> J[标签页面]
    J --> K[标签分类]
    A --> L[搜索页面]
    L --> M[搜索结果]
```

## 面包屑导航

大部分页面都包含清晰的面包屑导航，帮助用户了解当前位置：
- 首页 > 文章 > 文章标题
- 首页 > 归档 > 年份 > 月份
- 首页 > 分类 > 分类名称
