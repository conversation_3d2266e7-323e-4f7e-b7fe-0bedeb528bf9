# 组件库设计规范

## 设计系统概述

### 设计原则
- **一致性**: 所有组件遵循统一的设计语言
- **可复用性**: 组件可在不同页面和场景中复用
- **可扩展性**: 组件支持自定义和扩展
- **可访问性**: 所有组件符合可访问性标准

## 基础组件

### 1. 按钮组件 (Button)
```css
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}
```

**使用场景**: 提交表单、导航链接、操作触发

### 2. 链接组件 (Link)
```css
.link {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.link:hover {
    color: #0056b3;
    text-decoration: underline;
}
```

**使用场景**: 文本链接、导航链接、外部链接

### 3. 输入框组件 (Input)
```css
.input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}
```

**使用场景**: 搜索框、表单输入、用户交互

## 布局组件

### 1. 容器组件 (Container)
```css
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

@media (max-width: 768px) {
    .container {
        padding: 0 12px;
    }
}
```

### 2. 网格组件 (Grid)
```css
.grid {
    display: grid;
    gap: 16px;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3 {
        grid-template-columns: 1fr;
    }
}
```

### 3. 弹性布局组件 (Flex)
```css
.flex {
    display: flex;
}

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
```

## 内容组件

### 1. 文章卡片组件 (ArticleCard)
```css
.article-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #eee;
}

.article-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.article-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.article-card-content {
    padding: 16px;
}

.article-card-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.article-card-excerpt {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
}

.article-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
}
```

### 2. 标签组件 (Tag)
```css
.tag {
    display: inline-block;
    padding: 4px 8px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 12px;
    font-size: 12px;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.tag:hover {
    background-color: #e0e0e0;
    color: #333;
}

.tag-primary {
    background-color: #007bff;
    color: white;
}

.tag-primary:hover {
    background-color: #0056b3;
}
```

### 3. 个人信息卡片组件 (ProfileCard)
```css
.profile-card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    border: 1px solid #eee;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 16px;
    object-fit: cover;
}

.profile-name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.profile-bio {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 16px;
}

.profile-links {
    display: flex;
    justify-content: center;
    gap: 12px;
}
```

## 导航组件

### 1. 顶部导航组件 (Header)
```css
.header {
    background: white;
    border-bottom: 1px solid #eee;
    padding: 16px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-logo {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    text-decoration: none;
}

.header-menu {
    display: flex;
    gap: 24px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.header-menu-item {
    color: #666;
    text-decoration: none;
    transition: color 0.2s ease;
}

.header-menu-item:hover,
.header-menu-item.active {
    color: #333;
}
```

### 2. 面包屑组件 (Breadcrumb)
```css
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    font-size: 14px;
}

.breadcrumb-item {
    color: #666;
    text-decoration: none;
}

.breadcrumb-item:hover {
    color: #333;
}

.breadcrumb-separator {
    color: #ccc;
}

.breadcrumb-current {
    color: #333;
    font-weight: 500;
}
```

## 特殊效果组件

### 1. 动画文字组件 (AnimatedText)
```css
.animated-text-container {
    height: 50px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.animated-text {
    color: #fff;
    height: 50px;
    padding: 2px 15px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    animation: slide 6s linear infinite;
}

.animated-text-1 { background: lightcoral; }
.animated-text-2 { background: skyblue; }
.animated-text-3 { background: lightgreen; }
```

### 2. 渐变背景组件 (GradientBackground)
```css
.gradient-bg {
    background: linear-gradient(-45deg, coral, crimson, cyan, cornflowerblue, fuchsia);
    background-size: 1000% 1000%;
    animation: gradient 5s ease infinite;
    padding: 16px;
    border-radius: 8px;
    color: white;
    text-align: center;
}

@keyframes gradient {
    0% { background-position: 0 50%; }
    50% { background-position: 30% 50%; }
    100% { background-position: 0 50%; }
}
```

## 响应式设计规范

### 断点定义
```css
/* 移动端 */
@media (max-width: 767px) {
    /* 移动端样式 */
}

/* 平板端 */
@media (min-width: 768px) and (max-width: 1023px) {
    /* 平板端样式 */
}

/* 桌面端 */
@media (min-width: 1024px) {
    /* 桌面端样式 */
}
```

### 响应式组件示例
```css
.responsive-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 767px) {
    .responsive-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}
```

## 主题系统

### CSS变量定义
```css
:root {
    /* 颜色变量 */
    --color-primary: #007bff;
    --color-secondary: #6c757d;
    --color-success: #28a745;
    --color-danger: #dc3545;
    --color-warning: #ffc107;
    --color-info: #17a2b8;
    
    /* 文字颜色 */
    --text-primary: #333;
    --text-secondary: #666;
    --text-muted: #999;
    
    /* 背景颜色 */
    --bg-primary: #fff;
    --bg-secondary: #f8f9fa;
    --bg-muted: #e9ecef;
    
    /* 间距变量 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 字体变量 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
}
```

### 深色主题
```css
[data-theme="dark"] {
    --text-primary: #fff;
    --text-secondary: #ccc;
    --text-muted: #999;
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-muted: #404040;
}
```
