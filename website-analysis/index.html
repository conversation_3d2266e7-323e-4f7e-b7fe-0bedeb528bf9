<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在AI时代重新思考写作 | 深度洞察</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --text-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            --shadow-elegant: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .dark {
            --primary-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            --accent-gradient: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
            --text-gradient: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
        }

        body {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            line-height: 1.7;
            letter-spacing: 0.02em;
        }

        .font-serif {
            font-family: 'Noto Serif SC', Georgia, serif;
        }

        .gradient-text {
            background: var(--text-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-bg {
            background: var(--primary-gradient);
            position: relative;
            overflow: hidden;
        }

        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .card-elegant {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-elegant);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .dark .card-elegant {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .card-elegant:hover {
            transform: translateY(-4px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .section-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.4), transparent);
            margin: 4rem 0;
        }

        .dark .section-divider {
            background: linear-gradient(90deg, transparent, rgba(71, 85, 105, 0.6), transparent);
        }

        .drop-cap {
            float: left;
            font-size: 4rem;
            line-height: 3rem;
            padding-right: 0.5rem;
            padding-top: 0.25rem;
            font-weight: 700;
            color: #4f46e5;
        }

        .dark .drop-cap {
            color: #60a5fa;
        }

        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: var(--accent-gradient);
            z-index: 1000;
            transition: width 0.1s ease;
        }

        .floating-nav {
            position: fixed;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            z-index: 100;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .floating-nav.visible {
            opacity: 1;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(148, 163, 184, 0.4);
            margin: 0.5rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: #4f46e5;
            transform: scale(1.2);
        }

        .dark .nav-dot.active {
            background: #60a5fa;
        }

        .quote-highlight {
            position: relative;
            padding: 1.5rem;
            margin: 2rem 0;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(124, 58, 237, 0.05));
            border-left: 4px solid #4f46e5;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .dark .quote-highlight {
            background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1));
            border-left-color: #60a5fa;
        }

        .mermaid {
            background: transparent !important;
        }

        .comparison-card {
            position: relative;
            overflow: hidden;
        }

        .comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-gradient);
        }

        @media (max-width: 768px) {
            .floating-nav {
                display: none;
            }
            
            .drop-cap {
                font-size: 3rem;
                line-height: 2.5rem;
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stagger-1 { animation-delay: 0.1s; }
        .stagger-2 { animation-delay: 0.2s; }
        .stagger-3 { animation-delay: 0.3s; }
        .stagger-4 { animation-delay: 0.4s; }
    </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- Reading Progress Bar -->
    <div class="reading-progress"></div>

    <!-- Floating Navigation -->
    <nav class="floating-nav">
        <div class="nav-dot" data-section="hero"></div>
        <div class="nav-dot" data-section="problems"></div>
        <div class="nav-dot" data-section="misconceptions"></div>
        <div class="nav-dot" data-section="workflow"></div>
        <div class="nav-dot" data-section="insights"></div>
        <div class="nav-dot" data-section="resources"></div>
    </nav>

    <!-- Theme Toggle -->
    <button id="theme-toggle" class="fixed top-6 right-6 z-50 p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group">
        <i class="fas fa-sun text-yellow-500 dark:hidden group-hover:rotate-180 transition-transform duration-300"></i>
        <i class="fas fa-moon text-blue-400 hidden dark:inline-block group-hover:rotate-180 transition-transform duration-300"></i>
    </button>

    <!-- Hero Section -->
    <section id="hero" class="hero-bg min-h-screen flex items-center justify-center relative">
        <div class="container mx-auto px-6 text-center text-white relative z-10">
            <div class="animate-fade-in opacity-0">
                <h1 class="text-5xl md:text-7xl font-bold mb-6 font-serif leading-tight">
                    在AI时代<br>
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300">重新思考写作</span>
                </h1>
                <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90 stagger-1 animate-fade-in opacity-0">
                    当机器生成的文字充斥着我们的阅读世界，如何在AI的协助下保持写作的本质与深度？
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center stagger-2 animate-fade-in opacity-0">
                    <div class="flex items-center gap-2 text-lg">
                        <i class="fas fa-clock text-yellow-300"></i>
                        <span>15分钟深度阅读</span>
                    </div>
                    <div class="flex items-center gap-2 text-lg">
                        <i class="fas fa-brain text-pink-300"></i>
                        <span>AI写作洞察</span>
                    </div>
                    <div class="flex items-center gap-2 text-lg">
                        <i class="fas fa-lightbulb text-blue-300"></i>
                        <span>实用写作技巧</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <i class="fas fa-chevron-down text-white text-2xl opacity-70"></i>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-16 max-w-4xl">
        
        <!-- Introduction -->
        <section class="mb-16 stagger-3 animate-fade-in opacity-0">
            <div class="card-elegant rounded-2xl p-8 md:p-12">
                <p class="text-lg md:text-xl leading-relaxed">
                    <span class="drop-cap font-serif">在</span>过去几年中，我撰写和审阅了多篇技术论文和博客文章。我经常遇到由大语言模型生成的文字，它们总是让人感觉有些"不对劲"——有时甚至让人不想继续阅读。与此同时，我发现使用LLM来起草初稿、总结复杂材料和重新表述混乱的想法具有巨大价值。
                </p>
                <p class="text-lg md:text-xl leading-relaxed mt-6">
                    这篇文章详细阐述了我对在机器生成内容占主导地位的世界中写作的一些思考。我将首先分析LLM工具产生的糟糕写作的常见模式，然后为一些经常被误认为"LLM风格"但实际上很有用的写作习惯进行辩护，最后分享我在写作和指导LLM时依赖的具体规则和公式。
                </p>
            </div>
        </section>

        <!-- Problems Section -->
        <section id="problems" class="mb-16">
            <div class="text-center mb-12 stagger-4 animate-fade-in opacity-0">
                <h2 class="text-4xl md:text-5xl font-bold mb-4 gradient-text font-serif">LLM写作的常见问题</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">识别机器生成文本的危险信号</p>
            </div>

            <!-- Problem Cards -->
            <div class="grid gap-8 md:gap-12">
                <!-- Problem 1 -->
                <div class="card-elegant rounded-2xl p-8 comparison-card stagger-1 animate-fade-in opacity-0">
                    <div class="flex items-start gap-4 mb-6">
                        <div class="bg-red-100 dark:bg-red-900/30 p-3 rounded-full">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">空洞的"总结"句式</h3>
                            <p class="text-gray-600 dark:text-gray-400">看似总结实则无意义的结尾</p>
                        </div>
                    </div>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-red-50 dark:bg-red-900/20 p-6 rounded-xl border-l-4 border-red-400">
                            <h4 class="font-semibold text-red-800 dark:text-red-300 mb-3 flex items-center gap-2">
                                <i class="fas fa-times-circle"></i>
                                糟糕示例
                            </h4>
                            <p class="italic text-red-700 dark:text-red-300">
                                "通过遵循这些步骤，我们实现了更好的性能。"<br>
                                "通过内化这些原则，你可以穿透噪音。"
                            </p>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 p-6 rounded-xl border-l-4 border-green-400">
                            <h4 class="font-semibold text-green-800 dark:text-green-300 mb-3 flex items-center gap-2">
                                <i class="fas fa-lightbulb"></i>
                                改进建议
                            </h4>
                            <p class="text-green-700 dark:text-green-300">
                                用具体的洞察或可思考的观点结尾，而不是空洞的总结。给读者留下真正有价值的思考。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Problem 2 -->
                <div class="card-elegant rounded-2xl p-8 comparison-card stagger-2 animate-fade-in opacity-0">
                    <div class="flex items-start gap-4 mb-6">
                        <div class="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-full">
                            <i class="fas fa-list text-orange-600 dark:text-orange-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">过度使用项目符号</h3>
                            <p class="text-gray-600 dark:text-gray-400">列表泛滥，缺乏连贯性</p>
                        </div>
                    </div>
                    
                    <div class="quote-highlight">
                        <i class="fas fa-quote-left text-2xl text-gray-400 mb-4"></i>
                        <p class="text-lg">
                            LLM经常过度使用项目符号，特别是嵌套列表。当想法相互关联或需要上下文时，段落通常比列表更有效。列表适用于并行且独立的项目，但连贯的思想需要流畅的叙述。
                        </p>
                    </div>
                </div>

                <!-- Problem 3 -->
                <div class="card-elegant rounded-2xl p-8 comparison-card stagger-3 animate-fade-in opacity-0">
                    <div class="flex items-start gap-4 mb-6">
                        <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full">
                            <i class="fas fa-wave-square text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">单调的句子节奏</h3>
                            <p class="text-gray-600 dark:text-gray-400">缺乏韵律感的写作</p>
                        </div>
                    </div>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-red-50 dark:bg-red-900/20 p-6 rounded-xl">
                            <h4 class="font-semibold text-red-800 dark:text-red-300 mb-3">单调示例</h4>
                            <p class="text-sm text-red-700 dark:text-red-300 leading-relaxed">
                                我们最近推出了一个对话式AI功能，让用户可以用简单的英语提问并根据他们的过往活动和当前会话获得回应。系统搜索帮助文章数据库，使用自定义评分函数对最相关的文章进行排名，并将顶级结果传递给语言模型以生成最终答案。我们花了数周时间优化每个步骤以保持延迟在300毫秒以下，包括缓存、修剪无关文章和调整提示模板。
                            </p>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 p-6 rounded-xl">
                            <h4 class="font-semibold text-green-800 dark:text-green-300 mb-3">改进版本</h4>
                            <p class="text-sm text-green-700 dark:text-green-300 leading-relaxed">
                                我们刚刚推出了新的对话式AI功能。它用简单的语言回答用户问题，使用当前会话的上下文。系统搜索帮助文章，用自定义排名函数评分，将顶级结果输入微调的语言模型，通过缓存、修剪和提示调优技术在300毫秒内运行。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Data Visualization Section -->
        <section class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold mb-4 gradient-text font-serif">写作质量分析框架</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">LLM写作问题的可视化分析</p>
            </div>

            <div class="card-elegant rounded-2xl p-8 mb-8">
                <div class="mermaid">
                    graph TD
                        A[LLM生成文本] --> B{质量评估}
                        B --> C[结构问题]
                        B --> D[内容问题]
                        B --> E[语言问题]
                        
                        C --> C1[空洞总结句]
                        C --> C2[过度使用列表]
                        C --> C3[错误主语选择]
                        
                        D --> D1[信息密度低]
                        D --> D2[过于模糊]
                        D --> D3[流畅但无理解]
                        
                        E --> E1[单调节奏]
                        E --> E2[指代不清]
                        E --> E3[术语错误]
                        
                        C1 --> F[改进策略]
                        C2 --> F
                        C3 --> F
                        D1 --> F
                        D2 --> F
                        D3 --> F
                        E1 --> F
                        E2 --> F
                        E3 --> F
                        
                        F --> G[人工审查]
                        F --> H[结构化重写]
                        F --> I[专业验证]
                        
                        style A fill:#e1f5fe
                        style F fill:#f3e5f5
                        style G fill:#e8f5e8
                        style H fill:#e8f5e8
                        style I fill:#e8f5e8
                </div>
            </div>
        </section>

        <!-- Misconceptions Section -->
        <section id="misconceptions" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold mb-4 gradient-text font-serif">被误解的写作技巧</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">这些技巧并非"AI风格"，而是有效的修辞工具</p>
            </div>

            <div class="grid gap-8">
                <!-- Good Pattern 1 -->
                <div class="card-elegant rounded-2xl p-8 stagger-1 animate-fade-in opacity-0">
                    <div class="flex items-start gap-4 mb-6">
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">有意的重复</h3>
                            <p class="text-gray-600 dark:text-gray-400">当重复有助于澄清或强化复杂概念时，它是有价值的</p>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-xl border-l-4 border-blue-400">
                        <p class="text-blue-800 dark:text-blue-300 italic">
                            "向量数据库存储嵌入，或者说是在数百个维度中捕获语义含义的数学表示。换句话说，向量数据库帮助找到在含义上'接近'的结果，而不仅仅是精确的文本匹配。"
                        </p>
                    </div>
                </div>

                <!-- Good Pattern 2 -->
                <div class="card-elegant rounded-2xl p-8 stagger-2 animate-fade-in opacity-0">
                    <div class="flex items-start gap-4 mb-6">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                            <i class="fas fa-map-signs text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">指示性短语</h3>
                            <p class="text-gray-600 dark:text-gray-400">"本质上"、"简而言之"等短语在内容密集时帮助读者重新定位</p>
                        </div>
                    </div>
                    
                    <div class="bg-indigo-50 dark:bg-indigo-900/20 p-6 rounded-xl border-l-4 border-indigo-400">
                        <p class="text-indigo-800 dark:text-indigo-300 italic">
                            "本质上，我们不是对整个文档进行分类，而是独立地对每个部分进行分类。"
                        </p>
                    </div>
                </div>

                <!-- Good Pattern 3 -->
                <div class="card-elegant rounded-2xl p-8 stagger-3 animate-fade-in opacity-0">
                    <div class="flex items-start gap-4 mb-6">
                        <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full">
                            <i class="fas fa-balance-scale text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">平行结构</h3>
                            <p class="text-gray-600 dark:text-gray-400">重复的节奏可以帮助组织相关想法，使句子更容易理解</p>
                        </div>
                    </div>
                    
                    <div class="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-xl border-l-4 border-purple-400">
                        <p class="text-purple-800 dark:text-purple-300 italic">
                            "系统在输入间扩展，在负载下保持响应，即使在嘈杂的提示下也返回一致的结果。"
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Workflow Section -->
        <section id="workflow" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold mb-4 gradient-text font-serif">我的AI协作写作流程</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">保持写作动力的实用策略</p>
            </div>

            <div class="card-elegant rounded-2xl p-8 mb-8">
                <div class="mermaid">
                    flowchart LR
                        A[构思大纲] --> B[向AI叙述故事]
                        B --> C[生成详细结构]
                        C --> D[自己写段落草稿]
                        D --> E{卡住了吗?}
                        E -->|是| F[AI协助完善]
                        E -->|否| G[继续写作]
                        F --> G
                        G --> H[重读和批评]
                        H --> I[针对性重写]
                        I --> J[最终审查]
                        
                        style A fill:#e3f2fd
                        style D fill:#fff3e0
                        style F fill:#f3e5f5
                        style I fill:#e8f5e8
                        style J fill:#fce4ec
                </div>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="card-elegant rounded-2xl p-6 stagger-1 animate-fade-in opacity-0">
                    <div class="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                        <i class="fas fa-comments text-blue-600 dark:text-blue-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">向模型叙述故事</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        像向同事解释一样"谈论"结构，让AI生成详细大纲。确保结构在推进前是稳固的。
                    </p>
                </div>

                <div class="card-elegant rounded-2xl p-6 stagger-2 animate-fade-in opacity-0">
                    <div class="bg-green-100 dark:bg-green-900/30 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                        <i class="fas fa-pen text-green-600 dark:text-green-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">亲自写段落</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        即使写得很粗糙，也要尝试自己写每个段落。如果卡住了，写个半成品版本让AI帮助完善。
                    </p>
                </div>

                <div class="card-elegant rounded-2xl p-6 stagger-3 animate-fade-in opacity-0">
                    <div class="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                        <i class="fas fa-edit text-purple-600 dark:text-purple-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">针对性重写策略</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        不要简单地要求"改进"，而是使用具体的修辞模式，如SWBST结构或主谓靠近原则。
                    </p>
                </div>
            </div>

            <!-- SWBST Framework -->
            <div class="card-elegant rounded-2xl p-8 mt-8 stagger-4 animate-fade-in opacity-0">
                <h3 class="text-2xl font-bold mb-6 flex items-center gap-3">
                    <i class="fas fa-book-open text-indigo-600 dark:text-indigo-400"></i>
                    SWBST叙事结构
                </h3>
                <div class="grid md:grid-cols-5 gap-4">
                    <div class="text-center">
                        <div class="bg-red-100 dark:bg-red-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                            <span class="font-bold text-red-600 dark:text-red-400">S</span>
                        </div>
                        <h4 class="font-semibold mb-1">Somebody</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">行动者</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                            <span class="font-bold text-orange-600 dark:text-orange-400">W</span>
                        </div>
                        <h4 class="font-semibold mb-1">Wanted</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">目标</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                            <span class="font-bold text-yellow-600 dark:text-yellow-400">B</span>
                        </div>
                        <h4 class="font-semibold mb-1">But</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">障碍</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                            <span class="font-bold text-green-600 dark:text-green-400">S</span>
                        </div>
                        <h4 class="font-semibold mb-1">So</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">应对</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                            <span class="font-bold text-blue-600 dark:text-blue-400">T</span>
                        </div>
                        <h4 class="font-semibold mb-1">Then</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">结果</p>
                    </div>
                </div>
                
                <div class="mt-6 bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
                    <h4 class="font-semibold mb-3">实际应用示例：</h4>
                    <p class="text-gray-700 dark:text-gray-300 italic">
                        "我们使用GPT-4进行摘要。我们想要流畅的答案，但它会产生虚假事实。所以我们添加了检索步骤。然后我们根据引用准确性重新排列输出。"
                    </p>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Key Insights -->
        <section id="insights" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold mb-4 gradient-text font-serif">核心洞察</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">AI时代写作的本质思考</p>
            </div>

            <div class="card-elegant rounded-2xl p-8 md:p-12">
                <div class="quote-highlight">
                    <i class="fas fa-quote-left text-3xl text-gray-400 mb-6"></i>
                    <p class="text-2xl md:text-3xl font-serif leading-relaxed mb-6">
                        "现在生成中等质量的文本很便宜——甚至在范围狭窄且定义明确时生成高质量文本也很便宜。但弄清楚要说什么、如何构建框架、何时以及如何深入，这仍然是困难的部分。"
                    </p>
                    <p class="text-xl text-gray-600 dark:text-gray-400">
                        这需要判断力，而这正是LLM（目前）无法为我做到的。
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-8 mt-12">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl">
                        <h3 class="text-xl font-bold mb-4 flex items-center gap-3">
                            <i class="fas fa-robot text-blue-600 dark:text-blue-400"></i>
                            AI的优势
                        </h3>
                        <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start gap-2">
                                <i class="fas fa-check text-green-500 mt-1"></i>
                                快速生成初稿
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="fas fa-check text-green-500 mt-1"></i>
                                总结复杂材料
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="fas fa-check text-green-500 mt-1"></i>
                                重新表述想法
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="fas fa-check text-green-500 mt-1"></i>
                                克服写作障碍
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl">
                        <h3 class="text-xl font-bold mb-4 flex items-center gap-3">
                            <i class="fas fa-brain text-purple-600 dark:text-purple-400"></i>
                            人类的价值
                        </h3>
                        <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start gap-2">
                                <i class="fas fa-star text-yellow-500 mt-1"></i>
                                确定写作目标
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="fas fa-star text-yellow-500 mt-1"></i>
                                构建内容框架
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="fas fa-star text-yellow-500 mt-1"></i>
                                判断深度需求
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="fas fa-star text-yellow-500 mt-1"></i>
                                保证价值密度
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reading Resources -->
        <section id="resources" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold mb-4 gradient-text font-serif">延伸阅读</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">深入理解AI时代写作的推荐资源</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="card-elegant rounded-2xl p-6 stagger-1 animate-fade-in opacity-0">
                    <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                        <i class="fas fa-book text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">《风格的要素》</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">威廉·斯特伦克 & E.B.怀特</p>
                    <p class="text-gray-700 dark:text-gray-300 text-sm">
                        经典的写作指南，强调简洁和清晰的重要性，在AI时代更加相关。
                    </p>
                </div>

                <div class="card-elegant rounded-2xl p-6 stagger-2 animate-fade-in opacity-0">
                    <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                        <i class="fas fa-pen-fancy text-green-600 dark:text-green-400"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">《写作这回事》</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">斯蒂芬·金</p>
                    <p class="text-gray-700 dark:text-gray-300 text-sm">
                        大师级作家分享写作的本质和创作过程，强调真实性和个人声音的重要性。
                    </p>
                </div>

                <div class="card-elegant rounded-2xl p-6 stagger-3 animate-fade-in opacity-0">
                    <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                        <i class="fas fa-brain text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">《认知负荷理论》</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">约翰·斯威勒</p>
                    <p class="text-gray-700 dark:text-gray-300 text-sm">
                        理解读者的认知处理过程，帮助优化信息呈现方式。
                    </p>
                </div>

                <div class="card-elegant rounded-2xl p-6 stagger-4 animate-fade-in opacity-0">
                    <div class="bg-red-100 dark:bg-red-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                        <i class="fas fa-chart-line text-red-600 dark:text-red-400"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">《数据可视化之美》</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">爱德华·塔夫特</p>
                    <p class="text-gray-700 dark:text-gray-300 text-sm">
                        学习如何清晰地呈现复杂信息，这在AI辅助写作中尤为重要。
                    </p>
                </div>

                <div class="card-elegant rounded-2xl p-6 stagger-5 animate-fade-in opacity-0">
                    <div class="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                        <i class="fas fa-robot text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">《人工智能时代的写作》</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Reid Hoffman</p>
                    <p class="text-gray-700 dark:text-gray-300 text-sm">
                        探讨AI如何改变写作过程，以及人类创造力的持续价值。
                    </p>
                </div>

                <div class="card-elegant rounded-2xl p-6 stagger-6 animate-fade-in opacity-0">
                    <div class="bg-indigo-100 dark:bg-indigo-900/30 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                        <i class="fas fa-lightbulb text-indigo-600 dark:text-indigo-400"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">《思考，快与慢》</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">丹尼尔·卡尼曼</p>
                    <p class="text-gray-700 dark:text-gray-300 text-sm">
                        理解人类思维过程，帮助写出更符合读者认知习惯的内容。
                    </p>
                </div>
            </div>
        </section>

        <!-- Final Quote -->
        <section class="mb-16">
            <div class="card-elegant rounded-2xl p-8 md:p-12 text-center">
                <div class="quote-highlight border-none bg-transparent">
                    <i class="fas fa-quote-left text-4xl text-gray-400 mb-6"></i>
                    <p class="text-2xl md:text-3xl font-serif leading-relaxed mb-6 gradient-text">
                        "好写作最重要的标志，特别是在LLM生成文本的时代，是贡献与长度相称。"
                    </p>
                    <p class="text-xl text-gray-600 dark:text-gray-400">
                        读者离开时感到时间花得值得，这是我努力达到的标准。
                    </p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 dark:bg-black text-white py-12">
        <div class="container mx-auto px-6 text-center">
            <div class="mb-8">
                <h3 class="text-2xl font-bold mb-4">分享这篇文章</h3>
                <div class="flex justify-center gap-4">
                    <button class="bg-blue-600 hover:bg-blue-700 p-3 rounded-full transition-colors duration-300">
                        <i class="fab fa-twitter text-xl"></i>
                    </button>
                    <button class="bg-blue-800 hover:bg-blue-900 p-3 rounded-full transition-colors duration-300">
                        <i class="fab fa-linkedin text-xl"></i>
                    </button>
                    <button class="bg-gray-600 hover:bg-gray-700 p-3 rounded-full transition-colors duration-300">
                        <i class="fas fa-link text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-8">
                <p class="text-gray-400">
                    © 2025 AI时代写作洞察 | 探索人机协作的写作未来
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Mermaid
        mermaid.initialize({ 
            theme: 'base',
            themeVariables: {
                primaryColor: '#4f46e5',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#6366f1',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#fef3c7'
            }
        });

        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to system preference
        const savedTheme = localStorage.getItem('theme');
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        const currentTheme = savedTheme || systemTheme;

        html.classList.toggle('dark', currentTheme === 'dark');

        themeToggle.addEventListener('click', () => {
            const isDark = html.classList.contains('dark');
            html.classList.toggle('dark', !isDark);
            localStorage.setItem('theme', isDark ? 'light' : 'dark');
            
            // Reinitialize mermaid with new theme
            const newTheme = isDark ? 'base' : 'dark';
            mermaid.initialize({ 
                theme: newTheme,
                themeVariables: isDark ? {
                    primaryColor: '#4f46e5',
                    primaryTextColor: '#1f2937',
                    primaryBorderColor: '#6366f1',
                    lineColor: '#6b7280',
                    secondaryColor: '#f3f4f6',
                    tertiaryColor: '#fef3c7'
                } : {
                    primaryColor: '#60a5fa',
                    primaryTextColor: '#f9fafb',
                    primaryBorderColor: '#3b82f6',
                    lineColor: '#9ca3af',
                    secondaryColor: '#374151',
                    tertiaryColor: '#1f2937'
                }
            });
            
            // Re-render mermaid diagrams
            document.querySelectorAll('.mermaid').forEach(element => {
                element.removeAttribute('data-processed');
            });
            mermaid.init();
        });

        // Reading Progress
        const progressBar = document.querySelector('.reading-progress');
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        });

        // Floating Navigation
        const floatingNav = document.querySelector('.floating-nav');
        const navDots = document.querySelectorAll('.nav-dot');
        const sections = document.querySelectorAll('section[id]');

        // Show/hide floating nav based on scroll
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            floatingNav.classList.toggle('visible', scrollTop > 200);
            
            // Update active nav dot
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;
                if (scrollTop >= sectionTop && scrollTop < sectionTop + sectionHeight) {
                    current = section.getAttribute('id');
                }
            });
            
            navDots.forEach(dot => {
                dot.classList.remove('active');
                if (dot.getAttribute('data-section') === current) {
                    dot.classList.add('active');
                }
            });
        });

        // Nav dot click handlers
        navDots.forEach(dot => {
            dot.addEventListener('click', () => {
                const sectionId = dot.getAttribute('data-section');
                const section = document.getElementById(sectionId);
                if (section) {
                    section.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all elements with animate-fade-in class
        document.querySelectorAll('.animate-fade-in').forEach(el => {
            observer.observe(el);
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add hover effects to cards
        document.querySelectorAll('.card-elegant').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Initialize animations on load
        window.addEventListener('load', () => {
            // Trigger hero animations
            document.querySelectorAll('.animate-fade-in').forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
