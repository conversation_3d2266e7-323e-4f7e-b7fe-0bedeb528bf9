# 设计系统规范文档

## 1. 设计令牌 (Design Tokens)

### 1.1 颜色系统 (Color System)

#### 主色调 (Primary Colors)
```css
:root {
  /* 品牌色 */
  --brand-primary: rgb(217, 137, 130);    /* 暖橙色 - 主要强调色 */
  --brand-secondary: rgb(230, 46, 77);    /* 红色 - 行动召唤色 */
  
  /* 中性色 */
  --neutral-white: rgb(255, 255, 255);    /* 纯白 */
  --neutral-dark: rgb(25, 28, 35);        /* 深色背景 */
  --neutral-gray: rgb(235, 237, 239);     /* 浅灰背景 */
  --neutral-text: rgb(28, 30, 31);        /* 主文字色 */
  
  /* 功能色 */
  --success: rgb(16, 160, 48);            /* 成功绿 */
  --error: rgb(208, 16, 16);              /* 错误红 */
  --warning: rgb(230, 46, 77);            /* 警告红 */
  --info: rgb(165, 171, 174);             /* 信息灰 */
}
```

#### 颜色使用规则
- **主色调**: 用于标题、链接、选中状态
- **中性色**: 用于背景、边框、次要文字
- **功能色**: 用于状态提示、反馈信息

### 1.2 字体系统 (Typography System)

#### 字体族 (Font Family)
```css
:root {
  --font-family-primary: color-emoji, "VLOOK Digital local", 
                         -apple-system, Roboto, "SF Pro Text", 
                         "SF Pro Display", "PingFang SC", 
                         "PingFang TC", "PingFang HK", 
                         "Segoe UI", SegoeUI, "Microsoft YaHei", 
                         微软雅黑, "Heiti SC", 黑体-简, SimHei, 黑体, 
                         "Microsoft JhengHei", 微軟正黑體;
}
```

#### 字号层级 (Font Size Scale)
```css
:root {
  --font-size-xs: 12px;      /* 辅助文字 */
  --font-size-sm: 14px;      /* 小文字 */
  --font-size-base: 16px;    /* 基础文字 */
  --font-size-lg: 20px;      /* 大文字/按钮 */
  --font-size-xl: 24px;      /* H3标题 */
  --font-size-2xl: 26px;     /* H2标题 */
  --font-size-3xl: 28px;     /* H1标题 */
}
```

#### 字重 (Font Weight)
```css
:root {
  --font-weight-normal: 400;  /* 正文 */
  --font-weight-bold: 700;    /* 标题/强调 */
}
```

#### 行高 (Line Height)
```css
:root {
  --line-height-tight: 1.2;   /* 标题 */
  --line-height-normal: 1.5;  /* 正文 */
  --line-height-loose: 1.8;   /* 段落 */
}
```

### 1.3 间距系统 (Spacing System)

#### 基础间距单位
```css
:root {
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
}
```

#### 组件间距规则
- **按钮内边距**: 8px 20px (垂直 水平)
- **卡片内边距**: 10px (小卡片) / 20px (大卡片)
- **标题间距**: H1(84px) / H2(78px) / H3(48px)

### 1.4 圆角系统 (Border Radius)

```css
:root {
  --radius-sm: 6px;      /* 小圆角 - 分段控制器 */
  --radius-md: 8px;      /* 中圆角 - 按钮 */
  --radius-lg: 12px;     /* 大圆角 - 辅助按钮 */
  --radius-xl: 16px;     /* 超大圆角 - 卡片 */
  --radius-full: 50%;    /* 圆形 - 圆形按钮 */
}
```

### 1.5 阴影系统 (Shadow System)

```css
:root {
  /* 浮动卡片阴影 */
  --shadow-float: rgba(0, 0, 0, 0.3) 0px 4px 16px 0px, 
                  rgba(255, 255, 255, 0.2) 0px 0px 0px 1px inset;
  
  /* 浅色卡片阴影 */
  --shadow-light: rgba(0, 0, 0, 0.3) 0px 4px 16px 0px, 
                  rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset;
  
  /* 边框阴影 */
  --shadow-border: rgb(165, 171, 174) 0px 0px 0px 2px;
}
```

## 2. 组件规范 (Component Specifications)

### 2.1 按钮规范 (Button Specs)

#### 主要按钮 (Primary Button)
```css
.btn-primary {
  height: 44px;
  padding: 8px 20px;
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  background-color: var(--brand-secondary);
  color: var(--neutral-white);
  border: 2px solid var(--neutral-white);
}
```

#### 工具按钮 (Tool Button)
```css
.btn-tool {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--brand-primary);
  border: none;
}

.btn-tool:hover {
  background-color: rgba(217, 137, 130, 0.1);
}

.btn-tool.active {
  background-color: var(--brand-primary);
  color: var(--neutral-dark);
}
```

### 2.2 卡片规范 (Card Specs)

#### 深色浮动卡片
```css
.card-dark-float {
  background-color: var(--neutral-dark);
  border-radius: var(--radius-xl);
  padding: var(--space-3);
  box-shadow: var(--shadow-float);
  position: fixed;
}
```

#### 浅色信息卡片
```css
.card-light-info {
  background-color: var(--neutral-gray);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  box-shadow: var(--shadow-light);
}
```

### 2.3 导航规范 (Navigation Specs)

#### 固定工具栏
```css
.toolbar-fixed {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 50px;
  z-index: 100;
}
```

#### 侧边栏
```css
.sidebar {
  position: fixed;
  right: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  z-index: 4000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.sidebar.open {
  transform: translateX(0);
}
```

## 3. 布局规范 (Layout Specifications)

### 3.1 网格系统 (Grid System)
- **容器最大宽度**: 1200px
- **内容区域**: 居中对齐，左右边距自适应
- **侧边栏宽度**: 280px (可收缩)

### 3.2 Z-index层级 (Z-index Layers)
```css
:root {
  --z-base: 1;
  --z-dropdown: 100;
  --z-sticky: 200;
  --z-fixed: 300;
  --z-modal-backdrop: 1000;
  --z-modal: 1001;
  --z-popover: 2000;
  --z-tooltip: 3000;
  --z-sidebar: 4000;
}
```

### 3.3 响应式断点 (Responsive Breakpoints)
```css
:root {
  --breakpoint-sm: 640px;   /* 手机 */
  --breakpoint-md: 768px;   /* 平板 */
  --breakpoint-lg: 1024px;  /* 笔记本 */
  --breakpoint-xl: 1280px;  /* 桌面 */
}
```

## 4. 动画规范 (Animation Specifications)

### 4.1 过渡动画 (Transitions)
```css
:root {
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

.transition-all {
  transition: all var(--transition-normal);
}
```

### 4.2 缓动函数 (Easing Functions)
```css
:root {
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}
```

## 5. 可访问性规范 (Accessibility Specifications)

### 5.1 颜色对比度
- **正文文字**: 对比度 ≥ 4.5:1
- **大文字/标题**: 对比度 ≥ 3:1
- **交互元素**: 对比度 ≥ 3:1

### 5.2 焦点状态
```css
.focusable:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}
```

### 5.3 触摸目标
- **最小触摸区域**: 44px × 44px
- **按钮间距**: 至少 8px

## 6. 使用指南 (Usage Guidelines)

### 6.1 颜色使用原则
1. 主色调用于重要信息和交互元素
2. 中性色用于背景和次要信息
3. 功能色仅用于状态反馈

### 6.2 字体使用原则
1. 标题使用粗体，正文使用常规字重
2. 保持合理的字号层级
3. 确保足够的行间距

### 6.3 间距使用原则
1. 使用8px基础网格系统
2. 保持一致的组件间距
3. 合理的内容密度

---

*此设计系统规范确保了整个网站的视觉一致性和用户体验*
